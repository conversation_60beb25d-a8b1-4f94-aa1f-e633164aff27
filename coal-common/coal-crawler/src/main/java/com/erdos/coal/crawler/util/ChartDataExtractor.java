package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.WaitUntilState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 专门用于提取煤易宝网站图表数据的工具类
 * 支持神华外购、CCI指数、CCTD指数三种数据类型的提取
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ChartDataExtractor {

    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");

    /**
     * 数据去重辅助类
     */
    private static class DataDeduplicator {
        private final Set<String> processedRawData = new HashSet<>();
        private final Map<String, Map<String, String>> uniqueData = new LinkedHashMap<>();
        private int processedCount = 0;

        public boolean addData(String date, String calorific, String price, String rawData) {
            processedCount++;

            // 检查原始数据是否已处理
            if (processedRawData.contains(rawData)) {
                return false;
            }

            // 检查是否为有效数据
            if (!isValidData(calorific, price)) {
                return false;
            }

            Map<String, String> dateData = uniqueData.computeIfAbsent(date, k -> new LinkedHashMap<>());

            // 检查是否已有相同的数据
            if (dateData.containsKey(calorific) && dateData.get(calorific).equals(price)) {
                return false;
            }

            // 添加新数据
            dateData.put(calorific, price);
            processedRawData.add(rawData);
            logger.debug("添加新数据: {} - {}={}", date, calorific, price);
            return true;
        }

        private boolean isValidData(String calorific, String price) {
            try {
                // 提取价格数字
                String priceNum = price.replaceAll("[^0-9]", "");
                if (priceNum.isEmpty()) return false;

                int priceValue = Integer.parseInt(priceNum);

                // 扩展价格范围验证（150-1200元，覆盖更多可能的价格）
                if (priceValue < 150 || priceValue > 1200) {
                    logger.debug("价格超出有效范围: {}", priceValue);
                    return false;
                }

                // 扩展热值验证，支持更多格式
                boolean validCalorific = calorific.contains("5500kCal") || calorific.contains("5000kCal") ||
                                       calorific.contains("4500kCal") || calorific.contains("5800kCal") ||
                                       calorific.contains("外购") || calorific.contains("神优") ||
                                       calorific.contains("5500") || calorific.contains("5000") ||
                                       calorific.contains("4500") || calorific.contains("5800");

                if (!validCalorific) {
                    logger.debug("热值格式无效: {}", calorific);
                    return false;
                }

                // 排除明显的无关数据
                String lowerCalorific = calorific.toLowerCase();
                String lowerPrice = price.toLowerCase();
                if (lowerCalorific.contains("app") || lowerCalorific.contains("下载") ||
                    lowerPrice.contains("app") || lowerPrice.contains("下载")) {
                    logger.debug("排除无关数据: {} = {}", calorific, price);
                    return false;
                }

                return true;

            } catch (Exception e) {
                logger.debug("数据验证异常: {}", e.getMessage());
                return false;
            }
        }

        public Map<String, Map<String, String>> getUniqueData() {
            return uniqueData;
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }

    /**
     * 提取煤易宝数据的主方法
     *
     * @param indexType 指数类型（SHENHUA/CCI/CCTD）
     * @return 提取到的数据，格式：Map<日期, Map<热值, 价格>>
     */
    public static Map<String, Map<String, String>> extractMeiyibaoData(IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000));

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"));

            Page page = context.newPage();
            page.setDefaultTimeout(120000);

            try {
                logger.info("开始提取{}数据...", indexType.getName());

                // 访问煤易宝网站
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));
                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 点击对应的标签页
                clickIndexTab(page, indexType);
                page.waitForTimeout(3000);

                // 从Canvas折线图提取数据
                result = extractDataFromCanvas(page);

                // 如果Canvas提取失败，尝试从页面文本提取
                // if (result.isEmpty()) {
                //     logger.info("Canvas提取失败，尝试从页面文本提取...");
                //     String pageText = page.textContent("body");
                //     result = extractDataFromText(pageText, indexType);
                // }

                logger.info("{}数据提取完成，共获取{}天的数据", indexType.getName(), result.size());

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
            }

            page.close();
            context.close();
            browser.close();

        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 兼容测试类的方法，返回CoalIndexDataDto列表格式
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> result = new ArrayList<>();

        Map<String, Map<String, String>> rawData = extractMeiyibaoData(indexType);

        // 转换数据格式
        for (Map.Entry<String, Map<String, String>> dateEntry : rawData.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String calorific = priceEntry.getKey();
                String price = priceEntry.getValue();

                try {
                    CoalIndexDataDto dto = new CoalIndexDataDto();

                    // 解析日期字符串为Date对象
                    Date dataDate = parseDate(date);
                    dto.setDataDate(dataDate);

                    // 解析热值
                    Integer calorificValue = parseCalorificValue(calorific);
                    dto.setCalorificValue(calorificValue);

                    // 解析价格
                    BigDecimal priceValue = new BigDecimal(price.replaceAll("[^0-9]", ""));
                    dto.setPrice(priceValue);

                    dto.setIndexType(indexType);
                    dto.setSourceUrl(BASE_URL);
                    result.add(dto);
                } catch (Exception e) {
                    logger.debug("转换数据格式失败: {}", e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 点击指定的指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabText = getTabText(indexType);
            logger.info("尝试点击{}标签页...", tabText);

            // 尝试多种选择器策略
            String[] selectors = {
                "text=" + tabText,
                "[data-tab='" + indexType.getCode().toLowerCase() + "']",
                "#tab-" + getTabId(indexType),
                ".tab-item:has-text('" + tabText + "')",
                "a:has-text('" + tabText + "')",
                "button:has-text('" + tabText + "')"
            };

            boolean clicked = false;
            for (String selector : selectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        page.click(selector);
                        page.waitForTimeout(3000);
                        logger.info("成功点击{}标签页", tabText);
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}失败: {}", selector, e.getMessage());
                }
            }

            if (!clicked) {
                logger.warn("所有选择器都失败，使用默认标签页");
            }

        } catch (Exception e) {
            logger.warn("点击{}标签页失败: {}", indexType.getName(), e.getMessage());
        }
    }

    /**
     * 获取标签页文本
     */
    private static String getTabText(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "神华外购指数";
            case CCI:
                return "CCI指数";
            case CCTD:
                return "CCTD指数";
            default:
                return "神华外购指数";
        }
    }

    /**
     * 获取标签页ID
     */
    private static String getTabId(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "1";
            case CCI:
                return "2";
            case CCTD:
                return "3";
            default:
                return "3";
        }
    }

    /**
     * 从Canvas折线图提取数据
     */
    private static Map<String, Map<String, String>> extractDataFromCanvas(Page page) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try {
            logger.info("开始从Canvas折线图提取数据...");

            // 首先尝试查找具有特定属性的canvas元素
            Locator targetCanvas = findTargetCanvas(page);
            if (targetCanvas == null) {
                logger.warn("未找到目标canvas元素");
                return result;
            }

            // 获取canvas的边界框
            BoundingBox canvasBounds = targetCanvas.boundingBox();
            if (canvasBounds == null) {
                logger.warn("无法获取canvas的边界框");
                return result;
            }

            logger.info("目标Canvas边界框: x={}, y={}, width={}, height={}",
                       canvasBounds.x, canvasBounds.y, canvasBounds.width, canvasBounds.height);

            // 获取canvas的详细属性信息
            logCanvasDetails(page, targetCanvas);

            // 等待图表加载完成
            page.waitForTimeout(3000);

            // 在canvas上模拟鼠标移动，尝试触发tooltip
            result = extractDataByMouseInteraction(page, targetCanvas, canvasBounds);

        } catch (Exception e) {
            logger.error("从Canvas提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 查找目标Canvas元素
     */
    private static Locator findTargetCanvas(Page page) {
        try {
            // 策略1: 查找具有data-zr-dom-id="zr_0"属性的canvas
            Locator canvasWithZrId = page.locator("canvas[data-zr-dom-id='zr_0']");
            if (canvasWithZrId.count() > 0) {
                logger.info("找到data-zr-dom-id='zr_0'的canvas元素");
                return canvasWithZrId.first();
            }

            // 策略2: 查找coal-char类下的canvas
            Locator coalCharElement = page.locator(".coal-char");
            if (coalCharElement.count() > 0) {
                Locator canvasInCoalChar = coalCharElement.locator("canvas");
                if (canvasInCoalChar.count() > 0) {
                    logger.info("找到coal-char元素中的canvas，数量: {}", canvasInCoalChar.count());
                    return canvasInCoalChar.first();
                }
            }

            // 策略3: 查找所有canvas，选择尺寸最大的（通常是主图表）
            Locator allCanvases = page.locator("canvas");
            if (allCanvases.count() > 0) {
                logger.info("找到所有canvas元素，数量: {}", allCanvases.count());

                // 选择尺寸最大的canvas
                Locator largestCanvas = null;
                double maxArea = 0;

                for (int i = 0; i < allCanvases.count(); i++) {
                    Locator canvas = allCanvases.nth(i);
                    BoundingBox bounds = canvas.boundingBox();
                    if (bounds != null) {
                        double area = bounds.width * bounds.height;
                        logger.debug("Canvas[{}] 尺寸: {}x{}, 面积: {}", i, bounds.width, bounds.height, area);
                        if (area > maxArea) {
                            maxArea = area;
                            largestCanvas = canvas;
                        }
                    }
                }

                if (largestCanvas != null) {
                    logger.info("选择最大的canvas作为目标，面积: {}", maxArea);
                    return largestCanvas;
                }
            }

            logger.warn("未找到任何可用的canvas元素");
            return null;

        } catch (Exception e) {
            logger.error("查找目标canvas失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录Canvas的详细信息
     */
    private static void logCanvasDetails(Page page, Locator canvas) {
        try {
            // 获取canvas的属性
            String dataZrDomId = canvas.getAttribute("data-zr-dom-id");
            String width = canvas.getAttribute("width");
            String height = canvas.getAttribute("height");
            String style = canvas.getAttribute("style");

            logger.info("Canvas详细信息:");
            logger.info("  data-zr-dom-id: {}", dataZrDomId);
            logger.info("  width: {}", width);
            logger.info("  height: {}", height);
            logger.info("  style: {}", style);

        } catch (Exception e) {
            logger.debug("获取canvas详细信息失败: {}", e.getMessage());
        }
    }

    /**
     * 通过鼠标交互提取数据（使用去重辅助类）
     */
    private static Map<String, Map<String, String>> extractDataByMouseInteraction(
            Page page, Locator canvas, BoundingBox bounds) {
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始鼠标交互提取数据...");
            logger.info("Canvas实际尺寸: width={}, height={}", bounds.width, bounds.height);

            // 重新计算图表的有效区域（排除坐标轴、标题等）
            // 根据常见的ECharts布局，图表区域通常占canvas的60-80%
            double chartMarginLeft = bounds.width * 0.15;   // 左边距15%（Y轴标签）
            double chartMarginRight = bounds.width * 0.1;   // 右边距10%
            double chartMarginTop = bounds.height * 0.15;   // 上边距15%（标题）
            double chartMarginBottom = bounds.height * 0.2; // 下边距20%（X轴标签）

            double startX = bounds.x + chartMarginLeft;
            double endX = bounds.x + bounds.width - chartMarginRight;
            double startY = bounds.y + chartMarginTop;
            double endY = bounds.y + bounds.height - chartMarginBottom;

            logger.info("计算的图表区域: startX={}, endX={}, startY={}, endY={}",
                       startX, endX, startY, endY);

            // 增加采样密度，在图表区域内进行网格采样
            int xSamplePoints = 40; // X轴采样点数
            int ySamplePoints = 8;  // Y轴采样点数

            double stepX = (endX - startX) / xSamplePoints;
            double stepY = (endY - startY) / ySamplePoints;

            logger.info("采样参数: X轴{}个点，Y轴{}个点，总计{}个采样点",
                       xSamplePoints, ySamplePoints, xSamplePoints * ySamplePoints);

            int successCount = 0;
            int totalAttempts = 0;

            for (int i = 0; i <= xSamplePoints; i++) {
                double currentX = startX + i * stepX;

                for (int j = 0; j <= ySamplePoints; j++) {
                    double currentY = startY + j * stepY;
                    totalAttempts++;

                    try {
                        // 移动鼠标到当前位置
                        page.mouse().move(currentX, currentY);
                        page.waitForTimeout(200); // 减少等待时间提高效率

                        // 点击当前位置以触发数据显示
                        page.mouse().click(currentX, currentY);
                        page.waitForTimeout(300);

                        // 尝试提取tooltip数据
                        String tooltipData = extractTooltipData(page);
                        if (!tooltipData.isEmpty() && isValidCoalData(tooltipData)) {
                            logger.debug("在位置({:.1f}, {:.1f})提取到有效数据: {}", currentX, currentY, tooltipData);
                            parseTooltipData(tooltipData, deduplicator);
                            successCount++;
                        }

                        // 尝试使用JavaScript查找包含价格信息的元素
                        String jsData = extractDataUsingJavaScript(page);
                        if (!jsData.isEmpty() && isValidCoalData(jsData)) {
                            logger.debug("在位置({:.1f}, {:.1f})通过JavaScript提取到数据: {}", currentX, currentY, jsData);
                            parseTooltipData(jsData, deduplicator);
                            successCount++;
                        }

                        // 尝试从页面文本中提取当前显示的数据
                        String currentPageText = page.textContent("body");
                        extractCurrentDisplayData(currentPageText, deduplicator);

                        // 每10个采样点输出一次进度
                        if (totalAttempts % 50 == 0) {
                            logger.info("采样进度: {}/{}, 成功提取: {}", totalAttempts,
                                       (xSamplePoints + 1) * (ySamplePoints + 1), successCount);
                        }

                    } catch (Exception e) {
                        logger.debug("在位置({:.1f}, {:.1f})提取数据失败: {}", currentX, currentY, e.getMessage());
                    }
                }
            }

            logger.info("鼠标交互完成，总采样点: {}, 成功提取: {}, 处理了{}条原始数据，去重后提取到{}天的数据",
                       totalAttempts, successCount, deduplicator.getProcessedCount(), deduplicator.getUniqueData().size());

        } catch (Exception e) {
            logger.error("鼠标交互提取数据失败: {}", e.getMessage(), e);
        }

        return deduplicator.getUniqueData();
    }

    /**
     * 验证是否为有效的煤炭数据
     */
    private static boolean isValidCoalData(String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含煤炭相关关键词
        String lowerData = data.toLowerCase();
        return (lowerData.contains("kcal") || lowerData.contains("大卡") ||
                lowerData.contains("外购") || lowerData.contains("神优")) &&
               (lowerData.contains("元") || lowerData.contains("价格")) &&
               !lowerData.contains("app下载") && !lowerData.contains("下载");
    }

    /**
     * 使用JavaScript查找包含价格信息的元素
     */
    private static String extractDataUsingJavaScript(Page page) {
        try {
            // 使用JavaScript查找包含特定文本的元素
            String jsCode = "() => {" +
                "    const keywords = ['元', 'kCal', '外购', '神优', '大卡'];" +
                "    const results = [];" +
                "    " +
                "    // 查找所有文本节点" +
                "    const walker = document.createTreeWalker(" +
                "        document.body," +
                "        NodeFilter.SHOW_TEXT," +
                "        null," +
                "        false" +
                "    );" +
                "    " +
                "    let node;" +
                "    while (node = walker.nextNode()) {" +
                "        const text = node.textContent.trim();" +
                "        if (text.length > 5) {" +
                "            // 检查是否包含关键词" +
                "            const hasKeyword = keywords.some(keyword => text.includes(keyword));" +
                "            if (hasKeyword) {" +
                "                // 检查父元素是否可见" +
                "                const parent = node.parentElement;" +
                "                if (parent && parent.offsetParent !== null) {" +
                "                    results.push(text);" +
                "                }" +
                "            }" +
                "        }" +
                "    }" +
                "    " +
                "    // 查找具有特定类名的元素" +
                "    const classSelectors = [" +
                "        'coal-price', 'price-info', 'index-info', 'price-value', " +
                "        'coal-data', 'tooltip', 'chart-tooltip'" +
                "    ];" +
                "    " +
                "    classSelectors.forEach(className => {" +
                "        const elements = document.getElementsByClassName(className);" +
                "        for (let element of elements) {" +
                "            if (element.offsetParent !== null) {" +
                "                const text = element.textContent.trim();" +
                "                if (text.length > 5) {" +
                "                    results.push(text);" +
                "                }" +
                "            }" +
                "        }" +
                "    });" +
                "    " +
                "    // 查找包含价格模式的元素" +
                "    const allElements = document.querySelectorAll('*');" +
                "    for (let element of allElements) {" +
                "        if (element.offsetParent !== null && element.children.length === 0) {" +
                "            const text = element.textContent.trim();" +
                "            if (text.match(/\\\\d{3,}元|\\\\d{4,5}kCal|外购|神优/)) {" +
                "                results.push(text);" +
                "            }" +
                "        }" +
                "    }" +
                "    " +
                "    return results.filter((value, index, self) => self.indexOf(value) === index);" +
                "}";

            Object result = page.evaluate(jsCode);
            if (result instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> textList = (List<String>) result;

                // 查找最相关的数据
                for (String text : textList) {
                    if (isValidCoalData(text)) {
                        logger.debug("JavaScript找到有效数据: {}", text);
                        return text;
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("JavaScript提取数据失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 提取tooltip数据
     */
    private static String extractTooltipData(Page page) {
        // 安全的tooltip选择器列表（只使用标准CSS选择器）
        String[] tooltipSelectors = {
            ".tooltip",
            ".chart-tooltip",
            ".echarts-tooltip",
            "[class*='tooltip']",
            "[class*='tip']",
            ".popup",
            ".overlay",
            ".coal-tooltip",
            ".price-tooltip",
            ".index-tooltip",
            "[id*='tooltip']",
            "[id*='tip']",
            "div[style*='position: absolute'][style*='z-index']", // 绝对定位的浮动元素
            "div[style*='display: block'][style*='position']"     // 显示的定位元素
        };

        for (String selector : tooltipSelectors) {
            try {
                Locator tooltipElement = page.locator(selector);
                if (tooltipElement.count() > 0) {
                    for (int i = 0; i < tooltipElement.count(); i++) {
                        Locator element = tooltipElement.nth(i);
                        if (element.isVisible()) {
                            String text = element.textContent();
                            if (text != null && !text.trim().isEmpty() && isValidCoalData(text)) {
                                logger.debug("通过选择器 {} 找到tooltip数据: {}", selector, text.trim());
                                return text.trim();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.debug("提取tooltip失败，选择器: {}, 错误: {}", selector, e.getMessage());
            }
        }

        // 尝试使用Playwright的文本选择器（更安全的方式）
        try {
            String[] textPatterns = {
                "元",
                "kCal",
                "外购",
                "神优",
                "大卡"
            };

            for (String pattern : textPatterns) {
                try {
                    Locator elements = page.getByText(pattern);
                    if (elements.count() > 0) {
                        for (int i = 0; i < Math.min(elements.count(), 3); i++) {
                            Locator element = elements.nth(i);
                            if (element.isVisible()) {
                                String text = element.textContent();
                                if (text != null && isValidCoalData(text)) {
                                    logger.debug("通过文本模式 {} 找到数据: {}", pattern, text.trim());
                                    return text.trim();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("文本模式 {} 查找失败: {}", pattern, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.debug("文本模式查找失败: {}", e.getMessage());
        }



        // 最后尝试使用CSS类选择器
        try {
            String[] cssSelectors = {
                ".coal-price",
                ".price-info",
                ".index-info",
                ".price-value",
                ".coal-data",
                "[class*='price']",
                "[class*='coal']",
                "[class*='index']"
            };

            for (String cssSelector : cssSelectors) {
                try {
                    Locator elements = page.locator(cssSelector);
                    if (elements.count() > 0) {
                        for (int i = 0; i < Math.min(elements.count(), 3); i++) {
                            Locator element = elements.nth(i);
                            if (element.isVisible()) {
                                String text = element.textContent();
                                if (text != null && isValidCoalData(text)) {
                                    logger.debug("通过CSS选择器 {} 找到数据: {}", cssSelector, text.trim());
                                    return text.trim();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("CSS选择器 {} 失败: {}", cssSelector, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.debug("CSS选择器查找失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 解析tooltip数据
     */
    private static void parseTooltipData(String tooltipData, DataDeduplicator deduplicator) {
        try {
            logger.debug("开始解析tooltip数据: {}", tooltipData);

            // 多种日期格式匹配
            Pattern[] datePatterns = {
                Pattern.compile("(\\d{2}-\\d{2})"),           // 07-15
                Pattern.compile("(\\d{1,2}月\\d{1,2}日)"),      // 7月15日
                Pattern.compile("(\\d{4}-\\d{2}-\\d{2})"),    // 2025-07-15
                Pattern.compile("(\\d{2}/\\d{2})"),           // 07/15
            };

            String extractedDate = null;
            String remainingData = tooltipData;

            for (Pattern datePattern : datePatterns) {
                Matcher dateMatcher = datePattern.matcher(tooltipData);
                if (dateMatcher.find()) {
                    extractedDate = normalizeDateFormat(dateMatcher.group(1));
                    remainingData = tooltipData.substring(dateMatcher.end());
                    logger.debug("提取到日期: {} (原格式: {})", extractedDate, dateMatcher.group(1));
                    break;
                }
            }

            // 如果没有找到日期，尝试使用当前日期
            if (extractedDate == null) {
                extractedDate = DATE_FORMAT.format(new Date());
                logger.debug("未找到日期，使用当前日期: {}", extractedDate);
                remainingData = tooltipData;
            }

            // 解析价格数据 - 多种格式支持
            boolean foundData = false;

            // 格式1: 神华外购数据 - 外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元
            Pattern shenhuaPattern = Pattern.compile("外购(\\d{4,5}kCal|神优2)\\s*[=:]\\s*(\\d{3,})元");
            Matcher shenhuaMatcher = shenhuaPattern.matcher(remainingData);
            while (shenhuaMatcher.find()) {
                String calorific = "外购" + shenhuaMatcher.group(1);
                String price = shenhuaMatcher.group(2) + "元";
                deduplicator.addData(extractedDate, calorific, price, tooltipData);
                foundData = true;
                logger.debug("解析神华数据: {} = {}", calorific, price);
            }

            // 格式2: 标准数据 - 5500kCal=648元, 5000kCal=581元, 4500kCal=517元
            Pattern standardPattern = Pattern.compile("(\\d{4,5}kCal)\\s*[=:]\\s*(\\d{3,})元");
            Matcher standardMatcher = standardPattern.matcher(remainingData);
            while (standardMatcher.find()) {
                String calorific = standardMatcher.group(1);
                String price = standardMatcher.group(2) + "元";
                deduplicator.addData(extractedDate, calorific, price, tooltipData);
                foundData = true;
                logger.debug("解析标准数据: {} = {}", calorific, price);
            }

            // 格式3: 分离的格式 - 5500大卡 423元
            Pattern separatedPattern = Pattern.compile("(\\d{4,5})(?:大卡|kCal)\\s+(\\d{3,})元");
            Matcher separatedMatcher = separatedPattern.matcher(remainingData);
            while (separatedMatcher.find()) {
                String calorific = separatedMatcher.group(1) + "kCal";
                String price = separatedMatcher.group(2) + "元";
                deduplicator.addData(extractedDate, calorific, price, tooltipData);
                foundData = true;
                logger.debug("解析分离格式数据: {} = {}", calorific, price);
            }

            // 格式4: 如果以上都没匹配到，尝试智能解析
            if (!foundData) {
                parseIntelligentFormat(remainingData, extractedDate, deduplicator, tooltipData);
            }

        } catch (Exception e) {
            logger.debug("解析tooltip数据失败: {}", e.getMessage());
        }
    }

    /**
     * 标准化日期格式为MM-dd
     */
    private static String normalizeDateFormat(String dateStr) {
        try {
            if (dateStr.matches("\\d{2}-\\d{2}")) {
                return dateStr; // 已经是目标格式
            } else if (dateStr.matches("\\d{1,2}月\\d{1,2}日")) {
                // 7月15日 -> 07-15
                String[] parts = dateStr.replace("月", "-").replace("日", "").split("-");
                return String.format("%02d-%02d", Integer.parseInt(parts[0]), Integer.parseInt(parts[1]));
            } else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                // 2025-07-15 -> 07-15
                return dateStr.substring(5);
            } else if (dateStr.matches("\\d{2}/\\d{2}")) {
                // 07/15 -> 07-15
                return dateStr.replace("/", "-");
            }
        } catch (Exception e) {
            logger.debug("日期格式转换失败: {}", dateStr);
        }
        return dateStr;
    }

    /**
     * 智能解析格式
     */
    private static void parseIntelligentFormat(String data, String date, DataDeduplicator deduplicator, String originalData) {
        try {
            // 查找所有数字
            Pattern numberPattern = Pattern.compile("\\d{3,}");
            Matcher numberMatcher = numberPattern.matcher(data);

            List<String> numbers = new ArrayList<>();
            while (numberMatcher.find()) {
                numbers.add(numberMatcher.group());
            }

            // 如果找到3-4个数字，可能是价格数据
            if (numbers.size() >= 3 && numbers.size() <= 4) {
                String[] calorificValues = {"5500kCal", "5000kCal", "4500kCal", "外购神优2"};

                for (int i = 0; i < Math.min(numbers.size(), calorificValues.length); i++) {
                    String price = numbers.get(i) + "元";
                    // 验证价格范围
                    try {
                        int priceValue = Integer.parseInt(numbers.get(i));
                        if (priceValue >= 200 && priceValue <= 1000) {
                            deduplicator.addData(date, calorificValues[i], price, originalData + "_intelligent_" + i);
                            logger.debug("智能解析数据: {} = {}", calorificValues[i], price);
                        }
                    } catch (NumberFormatException e) {
                        logger.debug("价格数字解析失败: {}", numbers.get(i));
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("智能解析失败: {}", e.getMessage());
        }
    }

    /**
     * 提取当前显示的数据
     */
    private static void extractCurrentDisplayData(String pageText, DataDeduplicator deduplicator) {
        try {
            // 查找当前页面中显示的价格信息
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.length() > 5 && isValidCoalData(line)) {

                    // 多种日期格式的神华外购数据
                    Pattern[] shenhuaPatterns = {
                        Pattern.compile("(\\d{2}-\\d{2}).*?外购(\\d{4,5}kCal|神优2).*?(\\d{3,})元"),
                        Pattern.compile("(\\d{1,2}月\\d{1,2}日).*?外购(\\d{4,5}kCal|神优2).*?(\\d{3,})元"),
                        Pattern.compile("外购(\\d{4,5}kCal|神优2).*?(\\d{3,})元.*?(\\d{2}-\\d{2})")
                    };

                    for (Pattern pattern : shenhuaPatterns) {
                        Matcher matcher = pattern.matcher(line);
                        if (matcher.find()) {
                            String date = normalizeDateFormat(matcher.group(1));
                            String calorific = "外购" + matcher.group(2);
                            String price = matcher.group(3) + "元";
                            deduplicator.addData(date, calorific, price, line);
                            logger.debug("从页面文本提取神华数据: {} {} = {}", date, calorific, price);
                            continue;
                        }
                    }

                    // 多种日期格式的标准数据
                    Pattern[] standardPatterns = {
                        Pattern.compile("(\\d{2}-\\d{2}).*?(\\d{4,5}kCal).*?(\\d{3,})元"),
                        Pattern.compile("(\\d{1,2}月\\d{1,2}日).*?(\\d{4,5}kCal).*?(\\d{3,})元"),
                        Pattern.compile("(\\d{4,5}kCal).*?(\\d{3,})元.*?(\\d{2}-\\d{2})")
                    };

                    for (Pattern pattern : standardPatterns) {
                        Matcher matcher = pattern.matcher(line);
                        if (matcher.find()) {
                            String date = normalizeDateFormat(matcher.group(1));
                            String calorific = matcher.group(2);
                            String price = matcher.group(3) + "元";
                            deduplicator.addData(date, calorific, price, line);
                            logger.debug("从页面文本提取标准数据: {} {} = {}", date, calorific, price);
                        }
                    }

                    // 尝试提取结构化的价格显示
                    extractStructuredPriceData(line, deduplicator);
                }
            }

        } catch (Exception e) {
            logger.debug("提取当前显示数据失败: {}", e.getMessage());
        }
    }

    /**
     * 提取结构化的价格数据
     */
    private static void extractStructuredPriceData(String line, DataDeduplicator deduplicator) {
        try {
            // 查找类似 "07-15: 外购5500kCal=423元, 外购5000kCal=368元" 的格式
            Pattern structuredPattern = Pattern.compile("(\\d{2}-\\d{2})\\s*[:：]\\s*(.+)");
            Matcher structuredMatcher = structuredPattern.matcher(line);

            if (structuredMatcher.find()) {
                String date = structuredMatcher.group(1);
                String priceInfo = structuredMatcher.group(2);

                // 解析价格信息部分
                Pattern pricePattern = Pattern.compile("(外购)?(\\d{4,5}kCal|神优2)\\s*[=:]\\s*(\\d{3,})元");
                Matcher priceMatcher = pricePattern.matcher(priceInfo);

                while (priceMatcher.find()) {
                    String prefix = priceMatcher.group(1) != null ? priceMatcher.group(1) : "";
                    String calorific = prefix + priceMatcher.group(2);
                    String price = priceMatcher.group(3) + "元";
                    deduplicator.addData(date, calorific, price, line);
                    logger.debug("提取结构化数据: {} {} = {}", date, calorific, price);
                }
            }
        } catch (Exception e) {
            logger.debug("提取结构化价格数据失败: {}", e.getMessage());
        }
    }

    /**
     * 从页面文本提取数据（备用方法）
     */
    private static Map<String, Map<String, String>> extractDataFromText(String pageText, IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始从页面文本提取数据...");

            String[] lines = pageText.split("\n");
            for (String line : lines) {
                line = line.trim();

                // 查找包含日期和价格信息的行
                if (line.contains("-") && (line.contains("kCal") || line.contains("元"))) {

                    // 根据不同的指数类型使用不同的解析策略
                    switch (indexType) {
                        case SHENHUA:
                            parseShenhuaLine(line, deduplicator);
                            break;
                        case CCI:
                        case CCTD:
                            parseStandardLine(line, deduplicator);
                            break;
                    }
                }
            }

            result = deduplicator.getUniqueData();
            logger.info("文本提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从文本提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析神华外购格式的行
     */
    private static void parseShenhuaLine(String line, DataDeduplicator deduplicator) {
        // 神华外购格式：07-15: 外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元
        Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
        Matcher dateMatcher = datePattern.matcher(line);

        if (dateMatcher.find()) {
            String date = dateMatcher.group(1);

            Pattern pricePattern = Pattern.compile("外购(\\d{4,5}kCal|神优2)=(\\d{3,})元");
            Matcher priceMatcher = pricePattern.matcher(line);

            while (priceMatcher.find()) {
                String calorific = "外购" + priceMatcher.group(1);
                String price = priceMatcher.group(2) + "元";
                deduplicator.addData(date, calorific, price, line);
            }
        }
    }

    /**
     * 解析标准格式的行
     */
    private static void parseStandardLine(String line, DataDeduplicator deduplicator) {
        // 标准格式：07-24: 5500kCal=648元, 5000kCal=581元, 4500kCal=517元
        Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
        Matcher dateMatcher = datePattern.matcher(line);

        if (dateMatcher.find()) {
            String date = dateMatcher.group(1);

            Pattern pricePattern = Pattern.compile("(\\d{4,5}kCal)=(\\d{3,})元");
            Matcher priceMatcher = pricePattern.matcher(line);

            while (priceMatcher.find()) {
                String calorific = priceMatcher.group(1);
                String price = priceMatcher.group(2) + "元";
                deduplicator.addData(date, calorific, price, line);
            }
        }
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        System.out.println("=== 煤易宝网站数据提取测试（优化版）===");
        System.out.println("目标数据格式:");
        System.out.println("神华数据: 07-15: 外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元");
        System.out.println("CCI数据: 07-24: 5500kCal=648元, 5000kCal=581元, 4500kCal=517元");
        System.out.println("CCTD数据: 07-24: 5500kCal=648元, 5000kCal=584元, 4500kCal=518元");
        System.out.println("========================================");

        try {
            // 测试神华外购数据提取
            System.out.println("\n1. 测试神华外购数据提取:");
            long startTime = System.currentTimeMillis();
            Map<String, Map<String, String>> shenhuaData = extractMeiyibaoData(IndexType.SHENHUA);
            long endTime = System.currentTimeMillis();
            printData(shenhuaData, "神华外购");
            System.out.println("耗时: " + (endTime - startTime) + "ms");

            // 测试CCI指数数据提取
            System.out.println("\n2. 测试CCI指数数据提取:");
            startTime = System.currentTimeMillis();
            Map<String, Map<String, String>> cciData = extractMeiyibaoData(IndexType.CCI);
            endTime = System.currentTimeMillis();
            printData(cciData, "CCI指数");
            System.out.println("耗时: " + (endTime - startTime) + "ms");

            // 测试CCTD指数数据提取
            System.out.println("\n3. 测试CCTD指数数据提取:");
            startTime = System.currentTimeMillis();
            Map<String, Map<String, String>> cctdData = extractMeiyibaoData(IndexType.CCTD);
            endTime = System.currentTimeMillis();
            printData(cctdData, "CCTD指数");
            System.out.println("耗时: " + (endTime - startTime) + "ms");

            // 数据质量检查
            System.out.println("\n=== 数据质量检查 ===");
            checkDataQuality(shenhuaData, "神华外购");
            checkDataQuality(cciData, "CCI指数");
            checkDataQuality(cctdData, "CCTD指数");

        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查数据质量
     */
    private static void checkDataQuality(Map<String, Map<String, String>> data, String indexName) {
        if (data.isEmpty()) {
            System.out.println(indexName + ": 数据质量检查 - 无数据");
            return;
        }

        int totalEntries = 0;
        int validPrices = 0;

        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                totalEntries++;
                String calorific = priceEntry.getKey();
                String price = priceEntry.getValue();

                try {
                    int priceValue = Integer.parseInt(price.replaceAll("[^0-9]", ""));
                    if (priceValue >= 150 && priceValue <= 1200) {
                        validPrices++;
                    }
                } catch (NumberFormatException e) {
                    // 价格格式错误
                }
            }
        }

        double qualityRate = totalEntries > 0 ? (double) validPrices / totalEntries * 100 : 0;
        System.out.println(indexName + ": 数据质量检查 - 总条目: " + totalEntries +
                          ", 有效价格: " + validPrices + ", 质量率: " + String.format("%.1f%%", qualityRate));
    }

    /**
     * 打印数据结果
     */
    private static void printData(Map<String, Map<String, String>> data, String indexName) {
        if (data.isEmpty()) {
            System.out.println(indexName + ": 未提取到数据");
            return;
        }

        System.out.println(indexName + " 数据:");
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            StringBuilder sb = new StringBuilder();
            sb.append(date).append(": ");

            boolean first = true;
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                if (!first) sb.append(", ");
                sb.append(priceEntry.getKey()).append("=").append(priceEntry.getValue());
                first = false;
            }

            System.out.println(sb.toString());
        }
        System.out.println("共 " + data.size() + " 天的数据");
    }

    /**
     * 解析日期字符串为Date对象
     */
    private static Date parseDate(String dateStr) {
        try {
            // 假设当前年份，格式如：07-15
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);

            String fullDateStr = currentYear + "-" + dateStr;
            SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd");
            return fullFormat.parse(fullDateStr);
        } catch (Exception e) {
            logger.debug("解析日期失败: {}", dateStr);
            return new Date(); // 返回当前日期作为默认值
        }
    }

    /**
     * 解析热值字符串为Integer
     */
    private static Integer parseCalorificValue(String calorificStr) {
        try {
            if (calorificStr.contains("5500")) {
                return 5500;
            } else if (calorificStr.contains("5000")) {
                return 5000;
            } else if (calorificStr.contains("4500")) {
                return 4500;
            } else if (calorificStr.contains("神优2")) {
                return 5800; // 神优2通常对应5800大卡
            }
            return null;
        } catch (Exception e) {
            logger.debug("解析热值失败: {}", calorificStr);
            return null;
        }
    }
}
